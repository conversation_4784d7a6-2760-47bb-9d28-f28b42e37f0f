import Cookies from "js-cookie";
const ACCESS_TOKEN_KEY = "accessToken";

export const setAuthState = (user, response, rememberMe) => {
  if (rememberMe) {
    localStorage.setItem("usernameOrEmail", user.usernameOrEmail);

    sessionStorage.clear();
  } else {
    sessionStorage.setItem("usernameOrEmail", user.usernameOrEmail);

    localStorage.clear();
  }
};

export const isUserLoggedIn = () => {
  const logged =
    localStorage.getItem("usernameOrEmail") ||
    sessionStorage.getItem("usernameOrEmail");
  if (logged && logged !== "undefined" && logged !== "null" && logged !== "") {
    return true;
  } else {
    return false;
  }
};

export const setCookieAuthToken = (token, expiresInDays = 1) => {
  Cookies.set(ACCESS_TOKEN_KEY, token, {
    // expires: expiresInDays, // Default 1 day
    secure: true,
    sameSite: "strict",
    path: "/", // Accessible everywhere
  });
};

export const getCookieAuthToken = () => {
  return Cookies.get(ACCESS_TOKEN_KEY);
};

export const removeCookieAuthToken = () => {
  Cookies.remove(ACCESS_TOKEN_KEY, { path: "/" });
};

export function clearSessionStorage() {
  sessionStorage.clear();
  window.dispatchEvent(new Event("session-storage-cleared"));
}
